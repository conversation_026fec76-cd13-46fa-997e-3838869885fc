import { supabase } from '../lib/supabase';

/**
 * Interface for file information returned by directory listing
 */
export interface StorageFileInfo {
  name: string;
  path: string;
  size: number;
  created_at: string;
  updated_at: string;
  fieldName: string; // extracted from filename (verbrauchsrechnung1, verbrauchsrechnung2, etc.)
  originalName: string; // filename without field prefix
  fileType: 'pdf' | 'image' | 'other';
}

/**
 * Lists all files in a certificate directory
 * @param userId The user ID
 * @param certificateId The certificate ID
 * @returns Promise<StorageFileInfo[]> Array of file information
 */
export const listCertificateFiles = async (userId: string, certificateId: string): Promise<StorageFileInfo[]> => {
  try {
    if (!userId || !certificateId) {
      console.error('Invalid userId or certificateId provided');
      return [];
    }

    const directoryPath = `${userId}/${certificateId}`;

    const { data, error } = await supabase.storage
      .from('certificateuploads')
      .list(directoryPath, {
        limit: 100,
        sortBy: { column: 'name', order: 'asc' }
      });

    if (error) {
      console.error('Error listing certificate files:', error);
      return [];
    }

    if (!data) {
      return [];
    }

    // Process files and extract metadata
    const fileInfos: StorageFileInfo[] = data
      .filter(item => !item.name.endsWith('/')) // Filter out directories
      .map(file => {
        const fullPath = `${directoryPath}/${file.name}`;

        // Extract field name and original filename
        const { fieldName, originalName } = extractFileMetadata(file.name);

        // Determine file type
        const fileType = getFileTypeFromName(originalName);

        return {
          name: file.name,
          path: fullPath,
          size: file.metadata?.size || 0,
          created_at: file.created_at || '',
          updated_at: file.updated_at || '',
          fieldName,
          originalName,
          fileType
        };
      });

    return fileInfos;
  } catch (error) {
    console.error('Error in listCertificateFiles:', error);
    return [];
  }
};

/**
 * Extracts field name and original filename from storage filename
 * @param fileName The storage filename (e.g., "verbrauchsrechnung1_document.pdf")
 * @returns Object with fieldName and originalName
 */
export const extractFileMetadata = (fileName: string): { fieldName: string; originalName: string } => {
  // Match pattern: fieldName_originalName
  const match = fileName.match(/^(verbrauchsrechnung[123]|gebaeudebild)_(.+)$/);

  if (match) {
    return {
      fieldName: match[1],
      originalName: match[2]
    };
  }

  // Fallback for files that don't match the expected pattern
  return {
    fieldName: 'unknown',
    originalName: fileName
  };
};

/**
 * Determines file type from filename
 * @param fileName The filename
 * @returns File type category
 */
export const getFileTypeFromName = (fileName: string): 'pdf' | 'image' | 'other' => {
  const name = fileName.toLowerCase();

  if (name.endsWith('.pdf')) {
    return 'pdf';
  } else if (name.match(/\.(jpg|jpeg|png|webp|gif)$/)) {
    return 'image';
  }

  return 'other';
};

/**
 * Deletes a file from the certificateuploads storage bucket
 * @param filePath The file path to delete
 * @returns Promise<boolean> indicating success or failure
 */
export const deleteFile = async (filePath: string): Promise<boolean> => {
  try {
    if (!filePath || filePath.trim() === '') {
      console.error('Invalid file path provided for deletion');
      return false;
    }

    const { error } = await supabase.storage
      .from('certificateuploads')
      .remove([filePath]);

    if (error) {
      console.error('Error deleting file from storage:', error);
      return false;
    }

    console.log('Successfully deleted file:', filePath);
    return true;
  } catch (error) {
    console.error('Error in deleteFile:', error);
    return false;
  }
};

/**
 * Gets a signed URL for a file in the consolidated certificateuploads bucket
 * Handles both direct file paths and full URLs (including public URLs)
 *
 * @param path The file path or full URL
 * @returns A signed URL that can be used to access the file
 */
export const getSignedUrl = async (path: string): Promise<string | null> => {
  try {
    let filePath: string = '';
    const bucket = 'certificateuploads';

    // Check if path is a full URL or just a file path
    if (path.startsWith('http')) {
      // Extract the file path from the full URL
      // Expected URL format: https://[project].supabase.co/storage/v1/object/public/[bucket]/[file-path]
      const urlParts = path.split('/');

      // Find the 'public' segment first, then look for the bucket after it
      const publicIndex = urlParts.findIndex(part => part === 'public');

      if (publicIndex !== -1 && urlParts[publicIndex + 1] === bucket) {
        // Extract path after bucket in public URL
        // Everything after the bucket name is the file path
        filePath = urlParts.slice(publicIndex + 2).join('/');
      } else {
        // Try to find the bucket after 'object/sign/' for signed URLs
        const signIndex = urlParts.findIndex(part => part === 'sign');

        if (signIndex !== -1 && urlParts[signIndex + 1] === bucket) {
          // Extract path after bucket in signed URL
          filePath = urlParts.slice(signIndex + 2).join('/');
        } else {
          // Try to find the bucket directly in the URL
          const bucketIndex = urlParts.findIndex(part => part === bucket);
          if (bucketIndex !== -1) {
            // Extract the file path (everything after the bucket name)
            filePath = urlParts.slice(bucketIndex + 1).join('/');
          } else {
            console.error(`Invalid file path format or bucket not found in path:`, path);
            return null;
          }
        }
      }

      // Validate that we have a non-empty file path
      if (!filePath || filePath.trim() === '') {
        console.error('Extracted file path is empty for URL:', path);
        return null;
      }
    } else {
      // Path is already a file path
      filePath = path;
    }

    // Get a signed URL that will work for 60 minutes (3600 seconds)
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(filePath, 3600);

    if (error) {
      console.error('Error creating signed URL:', error);
      return null;
    }

    return data.signedUrl;
  } catch (error) {
    console.error('Error in getSignedUrl:', error);
    return null;
  }
};