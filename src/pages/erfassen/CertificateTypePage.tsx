import { useState } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { useQueryClient } from '@tanstack/react-query';
import { useCertificate } from '../../contexts/CertificateContext';

// Define the certificate types
type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

interface CertificateOption {
  id: CertificateType;
  title: string;
  description: string;
  pages: string[];
}

// Define the certificate options with their respective pages
const certificateOptions: CertificateOption[] = [
  {
    id: 'WG/V',
    title: 'Wohngebäude-Verbrauchsausweis',
    description: 'Für Wohngebäude basierend auf dem tatsächlichen Energieverbrauch',
    pages: [
      'objektdaten',
      'gebaeudedetails1',
      'gebaeudedetails2',
      'verbrauch',
      'zusammenfassung'
    ]
  },
  {
    id: 'WG/B',
    title: 'Wohngebäude-Bedarfsausweis',
    description: '<PERSON><PERSON><PERSON> Wohngebäude basierend auf dem berechneten Energiebedarf',
    pages: [
      'objektdaten',
      'gebaeudedetails1',
      'gebaeudedetails2',
      'fenster',
      'heizung',
      'tww-lueftung',
      'zusammenfassung'
    ]
  },
  {
    id: 'NWG/V',
    title: 'Nicht-Wohngebäude-Verbrauchsausweis',
    description: 'Für Nicht-Wohngebäude basierend auf dem tatsächlichen Energieverbrauch',
    pages: [
      'objektdaten',
      'gebaeudedetails1',
      'gebaeudedetails2',
      'verbrauch',
      'zusammenfassung'
    ]
  }
];

export const CertificateTypePage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [selectedType, setSelectedType] = useState<CertificateType | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const { createNewCertificate } = useCertificate();

  const handleCertificateSelection = (type: CertificateType) => {
    setSelectedType(type);
  };

  const handleContinue = async () => {
    if (!selectedType) {
      setError('Bitte wählen Sie einen Energieausweistyp aus.');
      return;
    }

    try {
      setIsCreating(true);
      // Create a new certificate with the selected type
      await createNewCertificate(selectedType);

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['energieausweise'] });

      // Navigate to the first data entry page
      navigate({ to: '/erfassen/objektdaten' });
    } catch (err: any) {
      setError(`Fehler beim Erstellen: ${err.message}`);
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Energieausweis-Typ auswählen
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Bitte wählen Sie den Typ des Energieausweises, den Sie erstellen möchten.
        Die Auswahl bestimmt, welche Daten Sie eingeben müssen.
      </p>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {certificateOptions.map((option) => (
          <div
            key={option.id}
            className={`bg-white p-6 rounded-lg shadow-md border-2 transition-all cursor-pointer ${
              selectedType === option.id
                ? 'border-green-500 shadow-lg'
                : 'border-gray-200 hover:shadow-lg hover:border-gray-300'
            }`}
            onClick={() => handleCertificateSelection(option.id)}
          >
            <h2 className="text-xl font-semibold text-gray-800 mb-3">{option.title}</h2>
            <p className="text-gray-600 mb-4">{option.description}</p>
            <div className={`w-6 h-6 rounded-full border-2 ${
              selectedType === option.id
                ? 'border-green-500 bg-green-500'
                : 'border-gray-300'
            } flex items-center justify-center`}>
              {selectedType === option.id && (
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-end">
        <button
          className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-lg font-medium disabled:bg-gray-400 disabled:cursor-not-allowed"
          onClick={handleContinue}
          disabled={!selectedType || isCreating}
        >
          {isCreating ? 'Wird erstellt...' : 'Weiter'}
        </button>
      </div>
    </div>
  );
};
