import { useState, useEffect } from 'react';
import { useForm, useField } from '@tanstack/react-form';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { Link, useNavigate } from '@tanstack/react-router';
import { supabase } from '../../lib/supabase';
import { useCertificate } from '../../contexts/CertificateContext';
import { CheckboxField } from '../../components/ui/CheckboxField';
import { Breadcrumb } from '../../components/ui/Breadcrumb';
import { usePageVisit } from '../../hooks/usePageVisit';

// Define interfaces for the JSON data from Supabase
interface TrinkwarmwasserData {
  TW_Solar?: string;
  HZ_Solar?: string;
  TW_WP?: string;
  HZ_WP?: string;
  [key: string]: any; // Allow other properties
}

interface HeizungData {
  Hzg_Baujahr?: string;
  [key: string]: any; // Allow other properties
}

// Define interface for gebaeudedetails2 data
interface Gebaeudedetails2Data {
  kuehlWfl?: string;
  Originaldaemmstandard?: string;
  bjFensterAustausch?: string;
  Fensterlüftung?: string;
  Schachtlüftung?: string;
  L_Mit_WRG?: string;
  L_Ohne_WRG?: string;
  Boden1_Dämmung?: string;
  Dach1_Dämmung?: string;
  Wand1_Dämmung?: string;
  boeden?: Bauteil[];
  daecher?: Bauteil[];
  waende?: Bauteil[];
  [key: string]: any; // Allow other properties
}

// Define the Bauteil schema for reuse
const bauteilSchema = z.object({
  id: z.string(),
  bezeichnung: z.string().min(1, 'Bezeichnung ist erforderlich'),
  massiv: z.string().optional(),
  uebergang: z.string().optional(),
  flaeche: z.string().min(1, 'Fläche ist erforderlich'),
  uWert: z.string().optional(),
  // Removed daemmung field as it's now tracked separately
});

// Define the form schema using Zod
const gebaeudedetails2Schema = z.object({
  // Gebäudedetails Teil 2
  kuehlWfl: z.string().default('0'),
  Originaldaemmstandard: z.enum(['0', '1', '2']).default('0'),
  bjFensterAustausch: z.string().optional(),
  Fensterlüftung: z.enum(['0', '1']).default('0'),
  Schachtlüftung: z.enum(['0', '1']).default('0'),
  L_Mit_WRG: z.enum(['0', '1']).default('0'),
  L_Ohne_WRG: z.enum(['0', '1']).default('0'),

  // Heizung Baujahr (für alle Zertifikatstypen)
  Hzg_Baujahr: z.string().optional(),

  // Erneuerbare Energien Felder (von TwwLueftungPage verschoben)
  TW_Solar: z.enum(['0', '1']).default('0'),
  HZ_Solar: z.enum(['0', '1']).default('0'),
  TW_WP: z.enum(['0', '1']).default('0'),
  HZ_WP: z.enum(['0', '1']).default('0'),

  // Dämmungsfelder für alle Zertifikatstypen
  Boden1_Dämmung: z.string().default('0'),
  Dach1_Dämmung: z.string().default('0'),
  Wand1_Dämmung: z.string().default('0'),

  // Bauteile (dynamisch)
  boeden: z.array(bauteilSchema).default([]),
  daecher: z.array(bauteilSchema).default([]),
  waende: z.array(bauteilSchema).default([]),
});

type Gebaeudedetails2FormValues = z.infer<typeof gebaeudedetails2Schema>;
type Bauteil = z.infer<typeof bauteilSchema>;

export const GebaeudedetailsPage2 = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [certificateType, setCertificateType] = useState<'WG/V' | 'WG/B' | 'NWG/V' | null>(null);

  // Mark this page as visited for navigation tracking
  usePageVisit('gebaeudedetails2');
  const { activeCertificateId } = useCertificate();

  // Default Bauteile
  const defaultBoeden = [
    { id: '1', bezeichnung: 'Boden1', massiv: 'kb_massiv', uebergang: '1', flaeche: '', uWert: '' }
  ];
  const defaultDaecher = [
    { id: '1', bezeichnung: 'Dach1', massiv: '0', uebergang: '0', flaeche: '', uWert: '' }
  ];
  const defaultWaende = [
    { id: '1', bezeichnung: 'Wand1', massiv: 'kb_zweischaligOhneDaemm', uebergang: '', flaeche: '', uWert: '' }
  ];

  // State für dynamische Bauteile
  const [boeden, setBoeden] = useState<Bauteil[]>(defaultBoeden);
  const [daecher, setDaecher] = useState<Bauteil[]>(defaultDaecher);
  const [waende, setWaende] = useState<Bauteil[]>(defaultWaende);

  // Initial form values
  const [initialValues, setInitialValues] = useState<Partial<Gebaeudedetails2FormValues>>(() => {
    // Base values that are always included
    const baseValues = {
      kuehlWfl: '0',
      Originaldaemmstandard: '0' as const,
      bjFensterAustausch: '',
      Fensterlüftung: '0' as const,
      Schachtlüftung: '0' as const,
      L_Mit_WRG: '0' as const,
      L_Ohne_WRG: '0' as const,
      // Heizung Baujahr für alle Zertifikatstypen
      Hzg_Baujahr: '',
      // Erneuerbare Energien Felder (von TwwLueftungPage verschoben)
      TW_Solar: '0' as const,
      HZ_Solar: '0' as const,
      TW_WP: '0' as const,
      HZ_WP: '0' as const,
      // Dämmungsfelder für alle Zertifikatstypen
      Boden1_Dämmung: '0',
      Dach1_Dämmung: '0',
      Wand1_Dämmung: '0',
    };

    // Only include building components if certificate type is WG/B
    if (certificateType === 'WG/B') {
      return {
        ...baseValues,
        boeden: boeden,
        daecher: daecher,
        waende: waende,
      };
    }

    return baseValues;
  });

  // Fetch existing data including certificate type, heizung, and trinkwarmwasser
  const { data: existingData, isError, error } = useQuery({
    queryKey: ['energieausweise', 'gebaeudedetails2', 'heizung', 'trinkwarmwasser', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('gebaeudedetails2, certificate_type, heizung, trinkwarmwasser')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Update form values when data is fetched
  useEffect(() => {
    if (existingData) {
      // Type assertion to ensure certificate_type matches the expected union type
      const certType = existingData.certificate_type as 'WG/V' | 'WG/B' | 'NWG/V';

      // Set certificate type if it exists
      if (existingData.certificate_type) {
        setCertificateType(certType);
      }

      // Extract renewable energy fields from trinkwarmwasser data
      // Type assertion to tell TypeScript about the structure
      const trinkwarmwasser = existingData.trinkwarmwasser as TrinkwarmwasserData | null;

      // Ensure values match the enum types defined in the schema
      const ensureEnumValue = (value: string | undefined): '0' | '1' =>
        (value === '1') ? '1' : '0';

      const renewableEnergyFields = {
        TW_Solar: ensureEnumValue(trinkwarmwasser?.TW_Solar),
        HZ_Solar: ensureEnumValue(trinkwarmwasser?.HZ_Solar),
        TW_WP: ensureEnumValue(trinkwarmwasser?.TW_WP),
        HZ_WP: ensureEnumValue(trinkwarmwasser?.HZ_WP),
      };

      if (existingData.gebaeudedetails2) {
        // Type assertion to tell TypeScript about the structure
        const data = existingData.gebaeudedetails2 as Gebaeudedetails2Data;

        // Update dynamic components based on certificate type
        if (certType === 'WG/B') {
          // Set building components if certificate type is WG/B
          if (data.boeden && Array.isArray(data.boeden)) {
            setBoeden(data.boeden);
          }

          if (data.daecher && Array.isArray(data.daecher)) {
            setDaecher(data.daecher);
          }

          if (data.waende && Array.isArray(data.waende)) {
            setWaende(data.waende);
          }
        } else {
          // Reset building components to empty arrays if not WG/B
          setBoeden([]);
          setDaecher([]);
          setWaende([]);
        }

        // Use the standalone dämmung fields or default to '0'
        const extractedDaemmungFields = {
          Boden1_Dämmung: data.Boden1_Dämmung || '0',
          Dach1_Dämmung: data.Dach1_Dämmung || '0',
          Wand1_Dämmung: data.Wand1_Dämmung || '0',
        };

        // Ensure enum values match the schema
        const ensureOriginaldaemmstandard = (value: string | undefined): '0' | '1' | '2' => {
          if (value === '1') return '1';
          if (value === '2') return '2';
          return '0';
        };

        // Create a sanitized version of the data with proper types
        const sanitizedData: Partial<Gebaeudedetails2FormValues> = {
          kuehlWfl: data.kuehlWfl || '0',
          Originaldaemmstandard: ensureOriginaldaemmstandard(data.Originaldaemmstandard),
          bjFensterAustausch: data.bjFensterAustausch || '',
          Fensterlüftung: ensureEnumValue(data.Fensterlüftung),
          Schachtlüftung: ensureEnumValue(data.Schachtlüftung),
          L_Mit_WRG: ensureEnumValue(data.L_Mit_WRG),
          L_Ohne_WRG: ensureEnumValue(data.L_Ohne_WRG),
        };

        // Update form values based on certificate type
        if (certType === 'WG/B') {
          setInitialValues(prev => ({
            ...prev,
            ...sanitizedData,
            ...extractedDaemmungFields,
            ...renewableEnergyFields,
            // Add Hzg_Baujahr from heizung data if available
            Hzg_Baujahr: (existingData.heizung as HeizungData | null)?.Hzg_Baujahr || '',
            boeden: data.boeden && Array.isArray(data.boeden) ? data.boeden : boeden,
            daecher: data.daecher && Array.isArray(data.daecher) ? data.daecher : daecher,
            waende: data.waende && Array.isArray(data.waende) ? data.waende : waende,
          }));
        } else {
          // Exclude building components if not WG/B, but keep the dämmung fields
          setInitialValues(prev => ({
            ...prev,
            ...sanitizedData,
            ...extractedDaemmungFields,
            ...renewableEnergyFields,
            // Add Hzg_Baujahr from heizung data if available
            Hzg_Baujahr: (existingData.heizung as HeizungData | null)?.Hzg_Baujahr || '',
          }));
        }
      }
    }
    setIsLoading(false);
  }, [existingData]);

  // Update initialValues when certificateType changes
  useEffect(() => {
    setInitialValues(prev => {
      // Preserve the dämmung fields regardless of certificate type
      const daemmungFields = {
        Boden1_Dämmung: prev.Boden1_Dämmung || '0',
        Dach1_Dämmung: prev.Dach1_Dämmung || '0',
        Wand1_Dämmung: prev.Wand1_Dämmung || '0',
      };

      // Ensure enum values are properly typed
      const ensureEnumValue = (value: string | undefined): '0' | '1' =>
        (value === '1') ? '1' : '0';

      const ensureOriginaldaemmstandard = (value: string | undefined): '0' | '1' | '2' => {
        if (value === '1') return '1';
        if (value === '2') return '2';
        return '0';
      };

      // Create a sanitized version of the previous state
      const sanitizedPrev: Partial<Gebaeudedetails2FormValues> = {
        kuehlWfl: prev.kuehlWfl || '0',
        Originaldaemmstandard: ensureOriginaldaemmstandard(prev.Originaldaemmstandard),
        bjFensterAustausch: prev.bjFensterAustausch || '',
        Fensterlüftung: ensureEnumValue(prev.Fensterlüftung),
        Schachtlüftung: ensureEnumValue(prev.Schachtlüftung),
        L_Mit_WRG: ensureEnumValue(prev.L_Mit_WRG),
        L_Ohne_WRG: ensureEnumValue(prev.L_Ohne_WRG),
        TW_Solar: ensureEnumValue(prev.TW_Solar),
        HZ_Solar: ensureEnumValue(prev.HZ_Solar),
        TW_WP: ensureEnumValue(prev.TW_WP),
        HZ_WP: ensureEnumValue(prev.HZ_WP),
        Hzg_Baujahr: prev.Hzg_Baujahr || '',
      };

      if (certificateType === 'WG/B') {
        return {
          ...sanitizedPrev,
          ...daemmungFields,
          boeden: boeden,
          daecher: daecher,
          waende: waende,
        };
      } else {
        // Create a new object without the building component properties
        // but keep the dämmung fields
        return {
          ...sanitizedPrev,
          ...daemmungFields,
        };
      }
    });
  }, [certificateType, boeden, daecher, waende]);

  // Define the mutation for saving data to Supabase
  const saveMutation = useMutation({
    mutationFn: async (data: Gebaeudedetails2FormValues) => {
      // Extract fields that need to be saved in different objects
      const {
        Hzg_Baujahr,
        TW_Solar,
        HZ_Solar,
        TW_WP,
        HZ_WP,
        ...gebaeudedetails2Data
      } = data;

      if (!activeCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      // First, get any existing heizung and trinkwarmwasser data
      const { data: existingData } = await supabase
        .from('energieausweise')
        .select('heizung, trinkwarmwasser')
        .eq('id', activeCertificateId)
        .single();

      // Prepare heizung data with Hzg_Baujahr
      const heizungData = {
        ...((existingData?.heizung as HeizungData | null) || {}),
        Hzg_Baujahr
      };

      // Prepare trinkwarmwasser data with renewable energy fields
      const trinkwarmwasserData = {
        ...((existingData?.trinkwarmwasser as TrinkwarmwasserData | null) || {}),
        TW_Solar,
        HZ_Solar,
        TW_WP,
        HZ_WP
      };

      // Update gebaeudedetails2, heizung, and trinkwarmwasser in one transaction
      const { data: result, error } = await supabase
        .from('energieausweise')
        .update({
          gebaeudedetails2: gebaeudedetails2Data,
          heizung: heizungData,
          trinkwarmwasser: trinkwarmwasserData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', activeCertificateId)
        .select();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['energieausweise', activeCertificateId] });
      queryClient.invalidateQueries({ queryKey: ['energieausweise', 'gebaeudedetails2', 'heizung', 'trinkwarmwasser', activeCertificateId] });
      // Navigate to the next page based on certificate type
      if (certificateType === 'WG/B') {
        navigate({ to: '/erfassen/fenster' });
      } else {
        // Skip tww-lueftung page for non-WG/B certificate types and go directly to verbrauch
        navigate({ to: '/erfassen/verbrauch' });
      }
    },
    onError: (error) => {
      setSubmitError(`Fehler beim Speichern: ${error.message}`);
    },
  });

  // Create the form
  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      setSubmitError(null);

      let dataToSubmit = { ...value };

      // Remove bjFensterAustausch field if certificate type is not WG/V
      if (certificateType !== 'WG/V' && 'bjFensterAustausch' in dataToSubmit) {
        const { bjFensterAustausch, ...rest } = dataToSubmit;
        dataToSubmit = rest;
      }

      // Remove kuehlWfl field if certificate type is NWG/V
      if (certificateType === 'NWG/V' && 'kuehlWfl' in dataToSubmit) {
        const { kuehlWfl, ...rest } = dataToSubmit;
        dataToSubmit = rest;
      }

      // For WG/B certificate type, we need to remove HZ_Solar, TW_WP, and HZ_WP fields
      if (certificateType === 'WG/B') {
        // Keep TW_Solar but remove the other renewable energy fields
        const { HZ_Solar, TW_WP, HZ_WP, ...rest } = dataToSubmit;
        dataToSubmit = rest;
      }

      // For non-WG/B certificate types, we need to:
      // 1. Remove the building component arrays
      // 2. Keep the specific dämmung fields
      if (certificateType !== 'WG/B') {
        // Extract the dämmung fields we want to keep
        const { Boden1_Dämmung, Dach1_Dämmung, Wand1_Dämmung } = dataToSubmit;

        // Remove boeden, daecher, and waende fields
        const { boeden, daecher, waende, ...rest } = dataToSubmit;

        // Create the final data object with the dämmung fields preserved
        dataToSubmit = {
          ...rest,
          Boden1_Dämmung,
          Dach1_Dämmung,
          Wand1_Dämmung,
        };
      }
      // No need to sync daemmung values since they're now only stored in the standalone fields

      saveMutation.mutate(dataToSubmit as Gebaeudedetails2FormValues);
    },
  });

  // Helper component for form fields
  const FormField = ({
    name,
    label,
    type = 'text',
    placeholder = '',
    required = true
  }: {
    name: keyof Gebaeudedetails2FormValues;
    label: string;
    type?: string;
    placeholder?: string;
    required?: boolean;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <input
          id={name}
          name={name}
          type={type}
          value={
            typeof state.value === 'boolean'
              ? String(state.value)
              : Array.isArray(state.value)
                ? '' // Don't try to display array values in a text input
                : (state.value ?? '')
          }
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          }`}
        />
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };

  // Helper component for select fields
  const SelectField = ({
    name,
    label,
    options,
    required = true
  }: {
    name: keyof Gebaeudedetails2FormValues;
    label: string;
    options: { value: string; label: string }[];
    required?: boolean;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <select
          id={name}
          name={name}
          value={
            typeof state.value === 'boolean'
              ? String(state.value)
              : Array.isArray(state.value)
                ? '' // Don't try to display array values in a select
                : (state.value ?? '')
          }
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          }`}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
        )}
      </div>
    );
  };

  // Bauteil-Komponente für dynamische Bauteile
  const BauteilField = ({
    index,
    type,
    onRemove
  }: {
    index: number;
    type: 'boden' | 'dach' | 'wand';
    onRemove: (index: number) => void;
  }) => {
    // Get the array field name based on type
    const arrayField = type === 'boden' ? 'boeden' : type === 'dach' ? 'daecher' : 'waende';

    // Use Tanstack Form's useField for each field
    const bezeichnungField = useField({
      name: `${arrayField}[${index}].bezeichnung` as const,
      form,
    });

    const massivField = useField({
      name: `${arrayField}[${index}].massiv` as const,
      form,
    });

    const uebergangField = type === 'boden' || type === 'dach' ? useField({
      name: `${arrayField}[${index}].uebergang` as const,
      form,
    }) : null;

    const flaecheField = useField({
      name: `${arrayField}[${index}].flaeche` as const,
      form,
    });

    const uWertField = useField({
      name: `${arrayField}[${index}].uWert` as const,
      form,
    });

    // We no longer need to access the daemmung field for each bauteil
    // as it's now only stored in the standalone fields

    return (
      <div className="p-4 mb-4 border rounded-md bg-gray-50">
        <div className="flex justify-between mb-2">
          <h4 className="font-medium">{type === 'boden' ? 'Boden' : type === 'dach' ? 'Dach' : 'Wand'} {index + 1}</h4>
          {index > 0 && (
            <button
              type="button"
              onClick={() => onRemove(index)}
              className="text-red-500 hover:text-red-700"
            >
              Entfernen
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="mb-2">
            <label htmlFor={`${type}-${index}-bezeichnung`} className="block text-sm font-medium text-gray-700 mb-1">
              Bezeichnung <span className="text-red-500">*</span>
            </label>
            <input
              id={`${type}-${index}-bezeichnung`}
              type="text"
              value={bezeichnungField.state.value ?? ''}
              onChange={(e) => bezeichnungField.handleChange(e.target.value)}
              onBlur={bezeichnungField.handleBlur}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
                bezeichnungField.state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder={`${type === 'boden' ? 'z.B. Kellerdecke' : type === 'dach' ? 'z.B. Kehlbalkendecke' : 'z.B. Außenwand'}`}
            />
            {bezeichnungField.state.meta.errors.length > 0 && (
              <p className="mt-1 text-sm text-red-500">{bezeichnungField.state.meta.errors.join(', ')}</p>
            )}
          </div>

          <div className="mb-2">
            <label htmlFor={`${type}-${index}-massiv`} className="block text-sm font-medium text-gray-700 mb-1">
              Material
            </label>
            <select
              id={`${type}-${index}-massiv`}
              value={massivField.state.value ?? ''}
              onChange={(e) => massivField.handleChange(e.target.value)}
              onBlur={massivField.handleBlur}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
                massivField.state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              {type === 'boden' && (
                <>
                  <option value="kb_massiv">Ziegel/Hohlstein</option>
                  <option value="kb_Holz">Holz</option>
                  <option value="kb_Stahlbeton">Stahlbeton</option>
                </>
              )}
              {type === 'dach' && (
                <>
                  <option value="1">Massivdecke</option>
                  <option value="0">Holzbalken</option>
                </>
              )}
              {type === 'wand' && (
                <>
                  <option value="kb_zweischaligOhneDaemm">Zweischalig ohne Dämmung</option>
                  <option value="kb_massiv">Ziegel/Hohlstein</option>
                  <option value="kb_Holz">Holz</option>
                  <option value="kb_Stahlbeton">Stahlbeton</option>
                  <option value="kb_zweischaligMitDaemm">Zweischalig mit Dämmung</option>
                </>
              )}
            </select>
            {massivField.state.meta.errors.length > 0 && (
              <p className="mt-1 text-sm text-red-500">{massivField.state.meta.errors.join(', ')}</p>
            )}
          </div>

          {(type === 'boden' || type === 'dach') && uebergangField && (
            <div className="mb-2">
              <label htmlFor={`${type}-${index}-uebergang`} className="block text-sm font-medium text-gray-700 mb-1">
                Übergang
              </label>
              <select
                id={`${type}-${index}-uebergang`}
                value={uebergangField.state.value ?? ''}
                onChange={(e) => uebergangField.handleChange(e.target.value)}
                onBlur={uebergangField.handleBlur}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  uebergangField.state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                {type === 'boden' && (
                  <>
                    <option value="1">Übergang zu unbeheiztem Keller</option>
                    <option value="0">Übergang zu Erdreich</option>
                  </>
                )}
                {type === 'dach' && (
                  <>
                    <option value="0">Übergang zu unbeheiztem Dachraum</option>
                    <option value="1">Direkt bewittert</option>
                  </>
                )}
              </select>
              {uebergangField.state.meta.errors.length > 0 && (
                <p className="mt-1 text-sm text-red-500">{uebergangField.state.meta.errors.join(', ')}</p>
              )}
            </div>
          )}

          <div className="mb-2">
            <label htmlFor={`${type}-${index}-flaeche`} className="block text-sm font-medium text-gray-700 mb-1">
              Fläche in m² <span className="text-red-500">*</span>
            </label>
            <input
              id={`${type}-${index}-flaeche`}
              type="text"
              value={flaecheField.state.value ?? ''}
              onChange={(e) => flaecheField.handleChange(e.target.value)}
              onBlur={flaecheField.handleBlur}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
                flaecheField.state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="z.B. 144"
            />
            {flaecheField.state.meta.errors.length > 0 && (
              <p className="mt-1 text-sm text-red-500">{flaecheField.state.meta.errors.join(', ')}</p>
            )}
          </div>

          <div className="mb-2">
            <label htmlFor={`${type}-${index}-uWert`} className="block text-sm font-medium text-gray-700 mb-1">
              U-Wert in W/m²K
            </label>
            <input
              id={`${type}-${index}-uWert`}
              type="text"
              value={uWertField.state.value ?? ''}
              onChange={(e) => uWertField.handleChange(e.target.value)}
              onBlur={uWertField.handleBlur}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
                uWertField.state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="z.B. 0,32"
            />
            {uWertField.state.meta.errors.length > 0 && (
              <p className="mt-1 text-sm text-red-500">{uWertField.state.meta.errors.join(', ')}</p>
            )}
          </div>

          {/* Dämmung field removed as it's now only stored in the standalone fields */}
        </div>
      </div>
    );
  };

  // Field references are not needed here as we use the FormField component directly

  // Funktionen zum Hinzufügen und Entfernen von Bauteilen
  const addBauteil = (type: 'boden' | 'dach' | 'wand') => {
    if (type === 'boden') {
      const newBoeden = [...boeden];
      const newId = (newBoeden.length + 1).toString();

      newBoeden.push({
        id: newId,
        bezeichnung: `Boden${newId}`,
        massiv: 'kb_massiv',
        uebergang: '1',
        flaeche: '',
        uWert: ''
      });
      setBoeden(newBoeden);
      form.setFieldValue('boeden', newBoeden);
    } else if (type === 'dach') {
      const newDaecher = [...daecher];
      const newId = (newDaecher.length + 1).toString();

      newDaecher.push({
        id: newId,
        bezeichnung: `Dach${newId}`,
        massiv: '0',
        uebergang: '0',
        flaeche: '',
        uWert: ''
      });
      setDaecher(newDaecher);
      form.setFieldValue('daecher', newDaecher);
    } else if (type === 'wand') {
      const newWaende = [...waende];
      const newId = (newWaende.length + 1).toString();

      newWaende.push({
        id: newId,
        bezeichnung: `Wand${newId}`,
        massiv: 'kb_massiv',
        uebergang: '',
        flaeche: '',
        uWert: ''
      });
      setWaende(newWaende);
      form.setFieldValue('waende', newWaende);
    }
  };

  const removeBauteil = (type: 'boden' | 'dach' | 'wand', index: number) => {
    if (type === 'boden') {
      const newBoeden = [...boeden];
      newBoeden.splice(index, 1);
      setBoeden(newBoeden);
      form.setFieldValue('boeden', newBoeden);
      // No need to sync daemmung values anymore
    } else if (type === 'dach') {
      const newDaecher = [...daecher];
      newDaecher.splice(index, 1);
      setDaecher(newDaecher);
      form.setFieldValue('daecher', newDaecher);
      // No need to sync daemmung values anymore
    } else if (type === 'wand') {
      const newWaende = [...waende];
      newWaende.splice(index, 1);
      setWaende(newWaende);
      form.setFieldValue('waende', newWaende);
      // No need to sync daemmung values anymore
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6" />

      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Gebäudedetails erfassen (Teil 2)
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Bitte geben Sie die Daten zur Gebäudehülle und Dämmung ein.
      </p>

      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="bg-white shadow-md rounded-lg p-6"
      >
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
            Kühlung und Lüftung
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {(certificateType === 'WG/V' || certificateType === 'WG/B') && (
              <FormField
                name="kuehlWfl"
                label="Kühlfläche in m²"
                placeholder="z.B. 25"
                required={false}
              />
            )}

            <SelectField
              name="Originaldaemmstandard"
              label="Originaldämmstandard"
              options={[
                { value: '0', label: 'Normal' },
                { value: '1', label: 'Niedrigenergiehaus' },
                { value: '2', label: 'Passivhaus' },
              ]}
              required={false}
            />

            {certificateType === 'WG/V' && (
              <FormField
                name="bjFensterAustausch"
                label="Jahr des Fensteraustauschs"
                placeholder="z.B. 2010"
                required={false}
              />
            )}

            <FormField
              name="Hzg_Baujahr"
              label="Baujahr Heizung"
              placeholder="z.B. 1995"
              required={false}
            />

            <CheckboxField
              name="Fensterlüftung"
              label="Fensterlüftung"
              form={form}
              required={false}
            />

            <CheckboxField
              name="Schachtlüftung"
              label="Schachtlüftung"
              form={form}
              required={false}
            />

            <CheckboxField
              name="L_Mit_WRG"
              label="Lüftungsanlage mit Wärmerückgewinnung"
              form={form}
              required={false}
            />

            <CheckboxField
              name="L_Ohne_WRG"
              label="Lüftungsanlage ohne Wärmerückgewinnung"
              form={form}
              required={false}
            />
          </div>
        </div>

        {/* Erneuerbare Energien Felder */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
            Erneuerbare Energien
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <CheckboxField
              name="TW_Solar"
              label="Trinkwarmwasser-Solaranlage"
              form={form}
              required={false}
            />

            {/* Only show HZ_Solar for WG/V and NWG/V certificate types */}
            {certificateType !== 'WG/B' && (
              <CheckboxField
                name="HZ_Solar"
                label="Heizungs-Solaranlage"
                form={form}
                required={false}
              />
            )}

            {/* Only show TW_WP for WG/V and NWG/V certificate types */}
            {certificateType !== 'WG/B' && (
              <CheckboxField
                name="TW_WP"
                label="Trinkwarmwasser-Wärmepumpe"
                form={form}
                required={false}
              />
            )}

            {/* Only show HZ_WP for WG/V and NWG/V certificate types */}
            {certificateType !== 'WG/B' && (
              <CheckboxField
                name="HZ_WP"
                label="Heizungs-Wärmepumpe"
                form={form}
                required={false}
              />
            )}
          </div>
        </div>

        {/* Dämmungsfelder für alle Zertifikatstypen */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
            Dämmung
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              name="Boden1_Dämmung"
              label="Boden Dämmung in cm"
              placeholder="z.B. 10"
              required={false}
            />

            <FormField
              name="Dach1_Dämmung"
              label="Dach Dämmung in cm"
              placeholder="z.B. 20"
              required={false}
            />

            <FormField
              name="Wand1_Dämmung"
              label="Wand Dämmung in cm"
              placeholder="z.B. 15"
              required={false}
            />
          </div>
        </div>

        {certificateType === 'WG/B' && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Böden
            </h2>
            {boeden.map((boden, index) => (
              <BauteilField
                key={boden.id}
                index={index}
                type="boden"
                onRemove={(index) => removeBauteil('boden', index)}
              />
            ))}
            <button
              type="button"
              onClick={() => addBauteil('boden')}
              className="mt-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
            >
              Boden hinzufügen
            </button>
          </div>
        )}

        {certificateType === 'WG/B' && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Dächer
            </h2>
            {daecher.map((dach, index) => (
              <BauteilField
                key={dach.id}
                index={index}
                type="dach"
                onRemove={(index) => removeBauteil('dach', index)}
              />
            ))}
            <button
              type="button"
              onClick={() => addBauteil('dach')}
              className="mt-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
            >
              Dach hinzufügen
            </button>
          </div>
        )}

        {certificateType === 'WG/B' && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Wände
            </h2>
            {waende.map((wand, index) => (
              <BauteilField
                key={wand.id}
                index={index}
                type="wand"
                onRemove={(index) => removeBauteil('wand', index)}
              />
            ))}
            <button
              type="button"
              onClick={() => addBauteil('wand')}
              className="mt-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
            >
              Wand hinzufügen
            </button>
          </div>
        )}

        {submitError && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {submitError}
          </div>
        )}

        <div className="flex justify-between mt-8">
          <Link
            to="/erfassen/gebaeudedetails1"
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
          >
            Zurück
          </Link>
          <button
            type="submit"
            disabled={form.state.isSubmitting || saveMutation.isPending}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
          >
            {form.state.isSubmitting || saveMutation.isPending ? 'Wird gespeichert...' : 'Weiter'}
          </button>
        </div>
      </form>
      )}

      {isError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <p>Fehler beim Laden der Daten: {error instanceof Error ? error.message : 'Unbekannter Fehler'}</p>
          <p className="mt-2">Bitte versuchen Sie es später erneut oder kontaktieren Sie den Support.</p>
        </div>
      )}
    </div>
  );
};