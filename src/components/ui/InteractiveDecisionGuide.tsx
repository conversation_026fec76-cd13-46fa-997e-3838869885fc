
const InteractiveDecisionGuide = () => {
  // State für den aktuellen Schritt und die Antworten
  const [step, setStep] = useState(1);
  const [answers, setAnswers] = useState({
    purpose: "",
    units: "",
    buildingYear: "",
    renovated: "",
    consumptionData: ""
  });
  const [result, setResult] = useState("");

  // Funktion zum Aktualisieren der Antworten und Fortschreiten zum nächsten Schritt
  const handleAnswer = (question, answer) => {
    const newAnswers = { ...answers, [question]: answer };
    setAnswers(newAnswers);
    
    // Logik zur Bestimmung des nächsten Schritts oder des Ergebnisses
    if (question === "purpose") {
      if (answer === "neubau") {
        // Bei Neubau/Modernisierung direkt zum Ergebnis
        setResult("bedarfsausweis");
        setStep(6);
      } else {
        setStep(2);
      }
    } else if (question === "units") {
      if (answer === "ab5") {
        // Bei >= 5 Wohneinheiten und Vermietung/Verkauf
        if (answers.purpose === "vermietung") {
          // Prüfen, ob Verbrauchswerte vorhanden sind
          setStep(5);
        }
      } else {
        // Bei < 5 Wohneinheiten
        setStep(3);
      }
    } else if (question === "buildingYear") {
      if (answer === "nach1977") {
        // Nach 1977 gebaut
        setStep(5);
      } else {
        // Vor 1977 gebaut
        setStep(4);
      }
    } else if (question === "renovated") {
      if (answer === "ja") {
        // Nach WSchV 77 saniert
        setStep(5);
      } else {
        // Nicht nach WSchV 77 saniert
        setResult("bedarfsausweis");
        setStep(6);
      }
    } else if (question === "consumptionData") {
      if (answer === "ja") {
        setResult("wahlfreiheit");
      } else {
        setResult("bedarfsausweis");
      }
      setStep(6);
    }
  };

  // Funktion zum Zurücksetzen des Formulars
  const resetForm = () => {
    setAnswers({
      purpose: "",
      units: "",
      buildingYear: "",
      renovated: "",
      consumptionData: ""
    });
    setResult("");
    setStep(1);
  };

  // Rendert die aktuelle Frage basierend auf dem Schritt
  const renderQuestion = () => {
    switch (step) {
      case 1:
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-6 text-center">Wofür benötigen Sie den Energieausweis?</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={() => handleAnswer("purpose", "vermietung")}
                className="bg-red-50 hover:bg-red-100 p-4 rounded-lg border-2 border-red-200 transition-colors flex flex-col items-center text-center"
              >
                <div className="bg-red-100 p-3 rounded-full mb-3">
                  <FileText className="h-6 w-6 text-red-600" />
                </div>
                <span className="font-semibold text-red-800">Vermietung / Verkauf / sonstiges</span>
                <p className="text-sm text-gray-600 mt-2">Für bestehende Gebäude, die vermietet oder verkauft werden sollen</p>
              </button>
              
              <button
                onClick={() => handleAnswer("purpose", "neubau")}
                className="bg-red-50 hover:bg-red-100 p-4 rounded-lg border-2 border-red-200 transition-colors flex flex-col items-center text-center"
              >
                <div className="bg-red-100 p-3 rounded-full mb-3">
                  <Home className="h-6 w-6 text-red-600" />
                </div>
                <span className="font-semibold text-red-800">Neubau / Modernisierung</span>
                <p className="text-sm text-gray-600 mt-2">Für Neubauten oder umfassend modernisierte Gebäude</p>
              </button>
            </div>
          </div>
        );
      
      case 2:
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-6 text-center">Wie viele Wohneinheiten hat Ihr Gebäude?</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={() => handleAnswer("units", "1bis4")}
                className="bg-red-50 hover:bg-red-100 p-4 rounded-lg border-2 border-red-200 transition-colors flex flex-col items-center text-center"
              >
                <span className="font-semibold text-red-800 text-xl">1-4 Wohneinheiten</span>
                <p className="text-sm text-gray-600 mt-2">Ein- bis Vierfamilienhaus</p>
              </button>
              
              <button
                onClick={() => handleAnswer("units", "ab5")}
                className="bg-red-50 hover:bg-red-100 p-4 rounded-lg border-2 border-red-200 transition-colors flex flex-col items-center text-center"
              >
                <span className="font-semibold text-red-800 text-xl">ab 5 Wohneinheiten</span>
                <p className="text-sm text-gray-600 mt-2">Mehrfamilienhaus oder größeres Gebäude</p>
              </button>
            </div>
            
            <div className="mt-6 flex justify-center">
              <button
                onClick={() => setStep(1)}
                className="flex items-center text-gray-600 hover:text-gray-800"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Zurück
              </button>
            </div>
          </div>
        );
      
      case 3:
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-6 text-center">Wann wurde Ihr Gebäude gebaut?</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={() => handleAnswer("buildingYear", "vor1977")}
                className="bg-red-50 hover:bg-red-100 p-4 rounded-lg border-2 border-red-200 transition-colors flex flex-col items-center text-center"
              >
                <span className="font-semibold text-red-800 text-xl">Vor dem 1.11.1977</span>
                <p className="text-sm text-gray-600 mt-2">Bauantrag vor der ersten Wärmeschutzverordnung</p>
              </button>
              
              <button
                onClick={() => handleAnswer("buildingYear", "nach1977")}
                className="bg-red-50 hover:bg-red-100 p-4 rounded-lg border-2 border-red-200 transition-colors flex flex-col items-center text-center"
              >
                <span className="font-semibold text-red-800 text-xl">Nach dem 1.11.1977</span>
                <p className="text-sm text-gray-600 mt-2">Bauantrag nach der ersten Wärmeschutzverordnung</p>
              </button>
            </div>
            
            <div className="mt-6 flex justify-center">
              <button
                onClick={() => setStep(2)}
                className="flex items-center text-gray-600 hover:text-gray-800"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Zurück
              </button>
            </div>
          </div>
        );
      
      case 4:
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-6 text-center">Wurde Ihr Gebäude nach der Wärmeschutzverordnung von 1977 saniert?</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={() => handleAnswer("renovated", "ja")}
                className="bg-red-50 hover:bg-red-100 p-4 rounded-lg border-2 border-red-200 transition-colors flex flex-col items-center text-center"
              >
                <span className="font-semibold text-red-800 text-xl">Ja</span>
                <p className="text-sm text-gray-600 mt-2">Das Gebäude wurde gemäß WSchV 77 oder höher saniert</p>
              </button>
              
              <button
                onClick={() => handleAnswer("renovated", "nein")}
                className="bg-red-50 hover:bg-red-100 p-4 rounded-lg border-2 border-red-200 transition-colors flex flex-col items-center text-center"
              >
                <span className="font-semibold text-red-800 text-xl">Nein</span>
                <p className="text-sm text-gray-600 mt-2">Das Gebäude wurde nicht oder nicht nach WSchV 77 saniert</p>
              </button>
            </div>
            
            <div className="mt-6 flex justify-center">
              <button
                onClick={() => setStep(3)}
                className="flex items-center text-gray-600 hover:text-gray-800"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Zurück
              </button>
            </div>
          </div>
        );
      
      case 5:
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-6 text-center">Sind Verbrauchswerte für die letzten drei Jahre vorhanden?</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={() => handleAnswer("consumptionData", "ja")}
                className="bg-red-50 hover:bg-red-100 p-4 rounded-lg border-2 border-red-200 transition-colors flex flex-col items-center text-center"
              >
                <span className="font-semibold text-red-800 text-xl">Ja</span>
                <p className="text-sm text-gray-600 mt-2">Verbrauchsdaten für Heizung und Warmwasser sind vorhanden</p>
              </button>
              
              <button
                onClick={() => handleAnswer("consumptionData", "nein")}
                className="bg-red-50 hover:bg-red-100 p-4 rounded-lg border-2 border-red-200 transition-colors flex flex-col items-center text-center"
              >
                <span className="font-semibold text-red-800 text-xl">Nein</span>
                <p className="text-sm text-gray-600 mt-2">Keine vollständigen Verbrauchsdaten vorhanden</p>
              </button>
            </div>
            
            <div className="mt-6 flex justify-center">
              <button
                onClick={() => {
                  // Zurück zum vorherigen Schritt basierend auf dem Pfad
                  if (answers.units === "ab5") {
                    setStep(2);
                  } else if (answers.buildingYear === "nach1977") {
                    setStep(3);
                  } else {
                    setStep(4);
                  }
                }}
                className="flex items-center text-gray-600 hover:text-gray-800"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Zurück
              </button>
            </div>
          </div>
        );
      
      case 6:
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-semibold mb-4">Ihr Ergebnis</h3>
              {result === "bedarfsausweis" ? (
                <div className="bg-red-100 p-6 rounded-lg inline-block">
                  <h4 className="text-xl font-bold text-red-800 mb-2">Energiebedarfsausweis</h4>
                  <p className="text-gray-700">Für Ihre Immobilie ist ein Energiebedarfsausweis gesetzlich vorgeschrieben.</p>
                </div>
              ) : (
                <div className="bg-yellow-100 p-6 rounded-lg inline-block">
                  <h4 className="text-xl font-bold text-yellow-800 mb-2">Wahlfreiheit</h4>
                  <p className="text-gray-700">Sie haben die Wahl zwischen einem Verbrauchs- oder Bedarfsausweis.</p>
                </div>
              )}
            </div>
            
            <div className="mt-8 space-y-4">
              <h4 className="font-semibold text-gray-800">Ihre Angaben:</h4>
              <ul className="space-y-2 text-gray-700">
                {answers.purpose && (
                  <li>
                    <span className="font-medium">Zweck:</span> {answers.purpose === "vermietung" ? "Vermietung/Verkauf" : "Neubau/Modernisierung"}
                  </li>
                )}
                {answers.units && (
                  <li>
                    <span className="font-medium">Wohneinheiten:</span> {answers.units === "1bis4" ? "1-4 Wohneinheiten" : "ab 5 Wohneinheiten"}
                  </li>
                )}
                {answers.buildingYear && (
                  <li>
                    <span className="font-medium">Baujahr:</span> {answers.buildingYear === "vor1977" ? "vor dem 1.11.1977" : "nach dem 1.11.1977"}
                  </li>
                )}
                {answers.renovated && (
                  <li>
                    <span className="font-medium">Nach WSchV 77 saniert:</span> {answers.renovated === "ja" ? "Ja" : "Nein"}
                  </li>
                )}
                {answers.consumptionData && (
                  <li>
                    <span className="font-medium">Verbrauchswerte vorhanden:</span> {answers.consumptionData === "ja" ? "Ja" : "Nein"}
                  </li>
                )}
              </ul>
            </div>
            
            <div className="mt-8 flex justify-center">
              <button
                onClick={resetForm}
                className="flex items-center justify-center gap-2 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors"
              >
                <RefreshCw className="h-4 w-4" />
                Neu starten
              </button>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="max-w-3xl mx-auto">
      {/* Fortschrittsanzeige */}
      {step < 6 && (
        <div className="mb-8">
          <div className="flex justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Schritt {step} von 5</span>
            <span className="text-sm font-medium text-gray-700">{Math.round((step / 5) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-red-600 h-2.5 rounded-full transition-all duration-300" 
              style={{ width: `${(step / 5) * 100}%` }}
            ></div>
          </div>
        </div>
      )}
      
      {/* Aktuelle Frage oder Ergebnis */}
      {renderQuestion()}
      
      {/* Hilfe-Box */}
      <div className="mt-8 bg-red-50 p-4 rounded-lg border border-red-200 flex items-start">
        <HelpCircle className="h-5 w-5 text-red-500 mr-3 flex-shrink-0 mt-0.5" />
        <div>
          <h4 className="font-semibold text-red-800 mb-1">Benötigen Sie Hilfe?</h4>
          <p className="text-sm text-gray-700">
            Wenn Sie unsicher sind, welcher Energieausweis für Ihre Immobilie der richtige ist, 
            kontaktieren Sie uns gerne für eine persönliche Beratung.
          </p>
          <Link to="/kontakt" className="text-sm text-red-600 hover:text-red-800 font-medium mt-2 inline-block">
            Kontakt aufnehmen
          </Link>
        </div>
      </div>
    </div>
  );
};

export default InteractiveDecisionGuide;
